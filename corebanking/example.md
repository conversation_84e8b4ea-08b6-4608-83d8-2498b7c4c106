# Mermaid 语法示例

## 1. 流程图 (Flowchart)

```mermaid
flowchart TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主页]
    B -->|否| D[跳转登录页]
    D --> E[输入用户名密码]
    E --> F{验证成功?}
    F -->|是| C
    F -->|否| G[显示错误信息]
    G --> E
    C --> H[结束]
```

## 2. 序列图 (Sequence Diagram)

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库

    U->>F: 登录请求
    F->>B: 发送登录信息
    B->>D: 查询用户信息
    D-->>B: 返回用户数据
    B-->>F: 返回登录结果
    F-->>U: 显示登录状态
```

## 3. 类图 (Class Diagram)

```mermaid
classDiagram
    class User {
        +String username
        +String email
        +String password
        +login()
        +logout()
        +updateProfile()
    }

    class Account {
        +String accountNumber
        +Double balance
        +String accountType
        +deposit(amount)
        +withdraw(amount)
        +getBalance()
    }

    class Transaction {
        +String transactionId
        +Date timestamp
        +Double amount
        +String type
        +execute()
    }

    User ||--o{ Account : owns
    Account ||--o{ Transaction : has
```

## 4. 状态图 (State Diagram)

```mermaid
stateDiagram-v2
    [*] --> 待处理
    待处理 --> 处理中 : 开始处理
    处理中 --> 已完成 : 处理成功
    处理中 --> 失败 : 处理失败
    失败 --> 待处理 : 重新提交
    已完成 --> [*]
```

## 5. 甘特图 (Gantt Chart)

```mermaid
gantt
    title 项目开发计划
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求收集           :done,    des1, 2024-01-01,2024-01-05
    需求分析           :done,    des2, after des1, 5d
    section 设计阶段
    系统设计           :active,  des3, 2024-01-10, 7d
    数据库设计         :         des4, after des3, 3d
    section 开发阶段
    前端开发           :         dev1, 2024-01-20, 10d
    后端开发           :         dev2, 2024-01-20, 12d
    section 测试阶段
    单元测试           :         test1, after dev1, 3d
    集成测试           :         test2, after dev2, 5d
```

## 6. 饼图 (Pie Chart)

```mermaid
pie title 用户分布
    "移动端用户" : 45
    "PC端用户" : 35
    "平板用户" : 15
    "其他" : 5
```

## 7. Git 图 (Git Graph)

```mermaid
gitgraph
    commit id: "初始提交"
    branch develop
    checkout develop
    commit id: "添加用户模块"
    commit id: "添加登录功能"
    checkout main
    merge develop
    commit id: "发布v1.0"
    branch feature/payment
    checkout feature/payment
    commit id: "添加支付功能"
    checkout develop
    merge feature/payment
    checkout main
    merge develop
    commit id: "发布v1.1"
```

## 8. 实体关系图 (Entity Relationship Diagram)

```mermaid
erDiagram
    USER ||--o{ ACCOUNT : has
    ACCOUNT ||--o{ TRANSACTION : contains
    USER {
        int user_id PK
        string username
        string email
        string password_hash
        datetime created_at
    }
    ACCOUNT {
        int account_id PK
        int user_id FK
        string account_number
        decimal balance
        string account_type
        datetime created_at
    }
    TRANSACTION {
        int transaction_id PK
        int account_id FK
        decimal amount
        string transaction_type
        string description
        datetime timestamp
    }
```

## 9. 用户旅程图 (User Journey)

```mermaid
journey
    title 用户注册流程
    section 访问网站
      访问首页: 5: 用户
      点击注册: 4: 用户
    section 填写信息
      输入用户名: 3: 用户
      输入邮箱: 3: 用户
      输入密码: 2: 用户
      确认密码: 2: 用户
    section 验证
      邮箱验证: 3: 用户, 系统
      手机验证: 2: 用户, 系统
    section 完成
      注册成功: 5: 用户, 系统
      跳转主页: 5: 用户
```

## 10. 思维导图 (Mind Map)

```mermaid
mindmap
  root((核心银行系统))
    用户管理
      用户注册
      用户登录
      权限管理
    账户管理
      开户
      销户
      账户查询
    交易处理
      转账
      存款
      取款
      交易记录
    风险控制
      反洗钱
      风险评估
      合规检查
```

## 常用语法说明

### 流程图方向
- `TD` 或 `TB`: 从上到下
- `BT`: 从下到上
- `LR`: 从左到右
- `RL`: 从右到左

### 节点形状
- `[]`: 矩形
- `()`: 圆角矩形
- `{}`: 菱形
- `(())`: 圆形
- `>]`: 旗帜形

### 连接线类型
- `-->`: 实线箭头
- `-.->`: 虚线箭头
- `==>`: 粗线箭头
- `--`: 实线
- `-.`: 虚线